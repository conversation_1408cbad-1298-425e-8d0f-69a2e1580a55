# 浮标实时状态更新功能测试指南

## 功能概述

本功能实现了浮标状态的实时更新，包括：
- 后端通过 WebSocket 推送浮标状态变化
- 前端建立 WebSocket 连接并监听消息
- 实时更新前端全局状态 BuoyContext
- 显示状态变化通知和监控面板

## 实现的功能

### 后端功能
1. **MQTT服务增强**
   - 处理浮标LWT（Last Will and Testament）消息
   - 处理心跳超时检测
   - 统一的状态变化广播机制

2. **WebSocket服务**
   - 广播到特定浮标主题：`/topic/buoys/{buoy_id}/data`
   - 广播到全局状态变化主题：`/topic/buoys/status_changes`
   - 支持状态变化消息格式

3. **心跳监控服务**
   - 定期检查浮标心跳状态
   - 自动将超时浮标标记为离线
   - 广播状态变化事件

### 前端功能
1. **BuoyContext增强**
   - 处理状态变化消息
   - 更新浮标状态
   - 显示状态变化通知
   - 订阅全局状态变化主题

2. **状态监控组件**
   - 实时显示状态变化事件
   - 显示事件来源和时间
   - 支持事件历史记录

3. **UI集成**
   - 在浮标列表侧边栏添加状态监控Tab
   - 实时状态指示器
   - Toast通知

## 测试步骤

### 1. 启动系统
```bash
# 启动后端
cd backend
python -m uvicorn fastapi_app.main:app --reload --host 0.0.0.0 --port 8000

# 启动前端
cd frontend
npm run dev
```

### 2. 启动浮标模拟器
```bash
cd simulator
python buoy_simulator.py
```

### 3. 测试场景

#### 场景1：浮标上线/下线
1. 启动浮标模拟器，观察前端状态变化
2. 停止浮标模拟器，观察离线状态
3. 重新启动浮标模拟器，观察上线状态

#### 场景2：心跳超时
1. 启动浮标模拟器
2. 强制终止模拟器进程（模拟网络断开）
3. 等待心跳超时（默认60秒）
4. 观察前端状态自动变为离线

#### 场景3：MQTT LWT消息
1. 启动浮标模拟器
2. 断开MQTT连接（模拟网络问题）
3. 观察LWT消息触发的状态变化

### 4. 验证点

#### 前端验证
1. **状态监控面板**
   - 打开浮标列表侧边栏
   - 切换到"状态监控"Tab
   - 观察实时状态变化事件

2. **浮标列表**
   - 观察浮标状态指示器颜色变化
   - 绿色：在线（active）
   - 黄色：离线（inactive）
   - 红色：错误（error）

3. **Toast通知**
   - 状态变化时显示通知
   - 包含浮标ID、状态、来源信息

#### 后端验证
1. **日志输出**
   - 查看MQTT服务日志
   - 查看WebSocket广播日志
   - 查看心跳监控日志

2. **WebSocket消息**
   - 使用浏览器开发者工具查看WebSocket消息
   - 验证消息格式和内容

## 消息格式

### 状态变化消息
```json
{
  "type": "status_change",
  "buoyId": "1",
  "timestamp": "2024-01-01T12:00:00Z",
  "data_type": "status",
  "value": "active",
  "previous_value": "inactive",
  "source": "mqtt_lwt"
}
```

### 心跳消息
```json
{
  "type": "heartbeat",
  "buoyId": "1",
  "timestamp": "2024-01-01T12:00:00Z",
  "data_type": "heartbeat",
  "value": "active",
  "source": "heartbeat"
}
```

## 故障排除

### 常见问题
1. **WebSocket连接失败**
   - 检查认证token是否有效
   - 检查WebSocket服务是否启动
   - 查看浏览器控制台错误

2. **状态更新不及时**
   - 检查MQTT连接状态
   - 检查心跳监控服务是否运行
   - 验证WebSocket订阅是否成功

3. **前端状态不更新**
   - 检查BuoyContext是否正确订阅
   - 验证消息处理逻辑
   - 查看浏览器控制台日志

### 调试技巧
1. **启用调试日志**
   - 后端：设置`IS_DEBUG=True`
   - 前端：打开浏览器开发者工具

2. **监控WebSocket消息**
   - 使用浏览器Network面板
   - 查看WebSocket连接和消息

3. **检查数据库状态**
   - 查询浮标表的状态字段
   - 验证last_heartbeat时间戳

## 扩展功能

### 可能的改进
1. **状态历史记录**
   - 存储状态变化历史
   - 提供状态变化统计

2. **告警机制**
   - 浮标离线告警
   - 状态异常通知

3. **批量操作**
   - 批量状态查询
   - 批量状态重置

4. **性能优化**
   - 消息去重
   - 连接池管理
   - 缓存机制
