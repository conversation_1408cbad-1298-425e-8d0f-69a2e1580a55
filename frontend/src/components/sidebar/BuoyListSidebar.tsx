// 浮标列表侧边栏组件
import React, { useState, useEffect } from 'react';
import { useBuoyContext } from '../../contexts/BuoyContext';
import { useTheme } from '../../contexts/ThemeContext';
import { AnalysisRequestItem } from '../../api/analysisService';
import { useAnalysisReport } from '../../hooks/useAnalysisReport';
import AnalysisReportPanel from '../analysis/AnalysisReportPanel';
import { useToast } from '../ui/Toast';
import { formatUTCToLocal } from '../../utils/dateUtils';

// 分隔面板图标（与 BuoyInfoSidebar 保持一致）
const SplitPanelIcon = ({ className = '' }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <rect width="18" height="18" x="3" y="3" rx="2"></rect>
    <path d="M15 3v18"></path>
  </svg>
);

interface BuoyListSidebarProps {
  className?: string;
  collapsed: boolean;
  toggleCollapsed: () => void;
}

/**
 * 浮标列表侧边栏组件
 * - 展示所有浮标
 * - 高亮当前选中浮标
 * - 支持点击切换选中浮标
 */
const BuoyListSidebar: React.FC<BuoyListSidebarProps> = ({
  className = '',
  collapsed,
  toggleCollapsed,
}) => {
  // 获取主题色
  const { theme, colors } = useTheme();
  const {
    buoys,
    selectedBuoyId,
    setSelectedBuoyId,
    loadingBuoys,
    sensorData,
  } = useBuoyContext();

  // Tab 状态：0-浮标列表，1-阈值配置
  const [activeTab, setActiveTab] = useState<number>(0);

  // 阈值本地存储（支持上限/下限可选）
  const [thresholds, setThresholds] = useState<{ [key: string]: { upper?: number | string; lower?: number | string } }>({});

  // 页面加载时读取 localStorage
  useEffect(() => {
    const saved = localStorage.getItem('sensorThresholds');
    if (saved) {
      try {
        setThresholds(JSON.parse(saved));
      } catch {
        setThresholds({});
      }
    }
  }, []);

  // 阈值变更时写入 localStorage
  useEffect(() => {
    localStorage.setItem('sensorThresholds', JSON.stringify(thresholds));
  }, [thresholds]);

  // 处理传感器类型与数值
  const sensorTypeMap: { [type: string]: { values: number[]; unit: string } } = {};
  sensorData.forEach((d) => {
    if (typeof d.value === 'number') {
      if (!sensorTypeMap[d.data_type]) {
        sensorTypeMap[d.data_type] = { values: [], unit: d.unit };
      }
      if (!sensorTypeMap[d.data_type].values.includes(d.value)) {
        sensorTypeMap[d.data_type].values.push(d.value);
      }
    }
  });
  // 排序
  Object.values(sensorTypeMap).forEach(obj => obj.values.sort((a, b) => a - b));

  // 折叠状态下的侧边栏
  if (collapsed) {
    return (
      <div
        className={`flex flex-col items-center ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} shadow-md h-full ${className}`}
      >
        <button
          onClick={toggleCollapsed}
          className={`p-2 my-2 rounded-md ${theme === 'dark'
            ? 'bg-gray-700 text-gray-100 hover:bg-gray-600'
            : 'bg-blue-50 text-blue-600 hover:bg-blue-100'
            }`}
          title="展开浮标列表"
        >
          <SplitPanelIcon className="text-xl" />
        </button>
        {selectedBuoyId && (
          <div
            className={`p-2 my-2 rounded-full ${buoys.find(b => b.id === selectedBuoyId)?.status === 'active'
              ? 'bg-green-400'
              : 'bg-yellow-400'
              }`}
            title={buoys.find(b => b.id === selectedBuoyId)?.name || '浮标'}
          />
        )}
      </div>
    );
  }

  return (
    <div
      className={`p-4 overflow-y-auto flex flex-col space-y-4 ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} shadow-md h-full ${className} relative`}
    >
      {/* 折叠按钮 */}
      <button
        onClick={toggleCollapsed}
        className={`absolute top-2 right-2 p-2 rounded-md ${theme === 'dark'
          ? 'bg-gray-700 text-gray-100 hover:bg-gray-600'
          : 'bg-blue-50 text-blue-600 hover:bg-blue-100'
          }`}
        title="收起浮标列表"
      >
        <SplitPanelIcon className="text-xl" />
      </button>

      {/* Tab 切换 */}
      <div className="flex space-x-2 mt-8 mb-2">
        <button
          className={`px-3 py-1 rounded-t ${activeTab === 0
            ? theme === 'dark' ? 'bg-gray-700 text-white' : 'bg-blue-100 text-blue-800'
            : theme === 'dark' ? 'bg-gray-900 text-gray-400' : 'bg-gray-200 text-gray-500'
            }`}
          onClick={() => setActiveTab(0)}
        >
          浮标列表
        </button>
        <button
          className={`px-3 py-1 rounded-t ${activeTab === 1
            ? theme === 'dark' ? 'bg-gray-700 text-white' : 'bg-blue-100 text-blue-800'
            : theme === 'dark' ? 'bg-gray-900 text-gray-400' : 'bg-gray-200 text-gray-500'
            }`}
          onClick={() => setActiveTab(1)}
        >
          阈值配置
        </button>
      </div>

      {/* Tab 内容 */}
      {activeTab === 0 ? (
        <>
          <h2 className={`text-xl font-bold mb-2 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>浮标列表</h2>
          {/* 加载中 */}
          {loadingBuoys ? (
            <div className="flex items-center justify-center h-32">
              <span className={`text-base ${theme === 'dark' ? 'text-gray-200' : 'text-gray-500'}`}>正在加载浮标列表...</span>
            </div>
          ) : buoys.length === 0 ? (
            <div className="flex items-center justify-center h-32">
              <span className={`text-base ${theme === 'dark' ? 'text-gray-200' : 'text-gray-500'}`}>暂无浮标数据</span>
            </div>
          ) : (
            <ul className="space-y-2">
              {buoys.map((buoy) => {
                const isSelected = buoy.id === selectedBuoyId;
                return (
                  <li
                    key={buoy.id}
                    className={`flex items-center p-3 rounded-lg cursor-pointer shadow transition
                      ${isSelected
                        ? theme === 'dark'
                          ? 'bg-blue-900 border border-blue-400'
                          : 'bg-blue-100 border border-blue-400'
                        : theme === 'dark'
                          ? 'hover:bg-gray-700'
                          : 'hover:bg-gray-50'
                      }
                    `}
                    onClick={() => setSelectedBuoyId(buoy.id)}
                    title={buoy.name}
                  >
                    {/* 状态圆点 */}
                    <span
                      className={`w-3 h-3 rounded-full mr-3 flex-shrink-0
                        ${buoy.status === 'active'
                          ? 'bg-green-400'
                          : buoy.status === 'inactive'
                            ? 'bg-yellow-400'
                            : 'bg-red-400'
                        }`}
                      title={buoy.status === 'active' ? '活跃' : buoy.status === 'inactive' ? '不活跃' : '异常'}
                    />
                    <div className="flex-1 min-w-0">
                      <div className={`font-medium truncate ${isSelected
                        ? theme === 'dark' ? 'text-blue-200' : 'text-blue-800'
                        : theme === 'dark' ? 'text-white' : 'text-gray-800'
                        }`}>
                        {buoy.name}
                      </div>
                      <div className={`text-xs truncate ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                        ID: {buoy.id}
                      </div>
                      <div className={`text-xs truncate ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                        最后心跳: {buoy.last_heartbeat ? new Date(buoy.last_heartbeat).toLocaleString() : '无'}
                      </div>
                    </div>
                  </li>
                );
              })}
            </ul>
          )}
        {/* 智能分析入口（迁移自 BuoyInfoSidebar） */}
        {sensorData.length > 0 && (
          <AnalysisSection sensorData={sensorData} />
        )}
        </>
      ) : (
        // 阈值配置 Tab
        <div>
          <h2 className={`text-xl font-bold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>阈值配置</h2>
          {Object.keys(sensorTypeMap).length === 0 ? (
            <div className="text-gray-400 text-center">暂无传感器数据</div>
          ) : (
            <div className="space-y-6">
              {Object.entries(sensorTypeMap).map(([type, { unit }]) => (
                <div key={type} className="border-b pb-4" style={{ color: colors.text.primary }}>
                  <div className="font-semibold mb-2 ">{type}（{unit}）</div>
                  {/* 移除历史数值标签，界面更简洁 */}
                  <div className="flex items-center gap-2">
                    <span className="text-sm" style={{ color: colors.text.primary }}>下限：</span>
                    <input
                      type="number"
                      className="border rounded px-2 py-1 w-24"
                      style={{ color: colors.text.primary, background: colors.background.main }}
                      value={thresholds[type]?.lower ?? ''}
                      onChange={e => {
                        const val = e.target.value;
                        setThresholds(prev => ({
                          ...prev,
                          [type]: {
                            ...prev[type],
                            lower: val
                          }
                        }));
                      }}
                      placeholder="可选"
                    />
                    <span className="text-sm" style={{ color: colors.text.primary }}>上限：</span>
                    <input
                      type="number"
                      className="border rounded px-2 py-1 w-24"
                      style={{ color: colors.text.primary, background: colors.background.main }}
                      value={thresholds[type]?.upper ?? ''}
                      onChange={e => {
                        const val = e.target.value;
                        setThresholds(prev => ({
                          ...prev,
                          [type]: {
                            ...prev[type],
                            upper: val
                          }
                        }));
                      }}
                      placeholder="可选"
                    />
                    <style>
                      {`
                        input::placeholder {
                          color: ${colors.text.secondary};
                          opacity: 1;
                        }
                      `}
                    </style>
                    {unit && (
                      <span
                        className="text-xs"
                        style={{ color: colors.text.secondary }}
                      >
                        {unit}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

/**
 * 智能分析功能区块（组合 hook、Selector、结果面板）
 */
const AnalysisSection: React.FC<{ sensorData: import('../../types/buoy.types').SensorData[] }> = ({ sensorData }) => {
  const { loading, error, data, fetchReport } = useAnalysisReport();

  // 触发分析
  const handleAnalysis = (items: AnalysisRequestItem[]) => {
    fetchReport({ items });
  };

  return (
    <div className="mt-6">
      <h3 className="text-lg font-semibold mb-2">智能分析</h3>
      <SensorAnalysisSelector
        sensorData={sensorData}
        onAnalysis={handleAnalysis}
        analysisLoading={loading}
      />
      <AnalysisReportPanel
        report={data}
        loading={loading}
        error={error}
      />
    </div>
  );
};

/**
 * 智能分析多选与按钮子组件（迁移自 BuoyInfoSidebar）
 */
interface SensorAnalysisSelectorProps {
  sensorData: import('../../types/buoy.types').SensorData[];
  onAnalysis: (items: AnalysisRequestItem[]) => void;
  analysisLoading?: boolean;
}
const SensorAnalysisSelector: React.FC<SensorAnalysisSelectorProps> = ({ sensorData, onAnalysis, analysisLoading }) => {
  // 改为按传感器类型选择
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const toast = useToast();
  const { selectedBuoyId } = useBuoyContext();

  // 获取所有传感器类型及其最新数据
  const sensorTypeMap = React.useMemo(() => {
    const map = new Map<string, { latestValue: number; unit: string; timestamp: string }>();
    sensorData.forEach(d => {
      if (typeof d.value === 'number') {
        const existing = map.get(d.data_type);
        if (!existing || new Date(d.timestamp) > new Date(existing.timestamp)) {
          map.set(d.data_type, {
            latestValue: d.value,
            unit: d.unit,
            timestamp: d.timestamp
          });
        }
      }
    });
    return map;
  }, [sensorData]);

  // 选择切换
  const handleSelect = (type: string) => {
    setSelectedTypes(prev =>
      prev.includes(type) ? prev.filter(t => t !== type) : [...prev, type]
    );
  };

  // 组装API请求体
  const buildRequestItems = (): AnalysisRequestItem[] => {
    if (!selectedBuoyId) {
      toast.showToast('请先选择一个浮标', 'warning');
      return [];
    }

    return sensorData
      .filter(d => selectedTypes.includes(d.data_type) && typeof d.value === 'number')
      .map(d => ({
        buoy_id: selectedBuoyId,
        sensor_type: d.data_type,
        data: {
          timestamp: d.timestamp,
          value: d.value as number,
          unit: d.unit,
        },
      }));
  };

  // 智能分析主流程
  const handleAnalysis = () => {
    if (!selectedBuoyId) {
      toast.showToast('请先选择一个浮标', 'warning');
      return;
    }
    if (selectedTypes.length === 0) {
      toast.showToast('请至少选择一个传感器类型', 'warning');
      return;
    }
    const items = buildRequestItems();
    if (items.length > 0) {
      onAnalysis(items);
      toast.showToast('正在分析，请稍候...', 'info');
    }
  };

  // 渲染多选列表
  return (
    <div className="mb-4">
      <div className="flex flex-wrap gap-2 mb-2">
        {Array.from(sensorTypeMap.entries()).map(([type, { latestValue, unit, timestamp }]) => (
          <label key={type} className="flex items-center space-x-1 text-sm px-3 py-2 rounded border cursor-pointer hover:bg-gray-50 transition-colors"
            style={{ 
              borderColor: selectedTypes.includes(type) ? '#3b82f6' : '#d1d5db', 
              background: selectedTypes.includes(type) ? '#e0e7ff' : 'transparent' 
            }}>
            <input
              type="checkbox"
              checked={selectedTypes.includes(type)}
              onChange={() => handleSelect(type)}
              disabled={analysisLoading}
              className="mr-2"
            />
            <div className="flex flex-col">
              <span className="font-medium">{type}</span>
              <span className="text-xs text-gray-500">
                最新值: {latestValue}{unit} ({formatUTCToLocal(timestamp)})
              </span>
            </div>
          </label>
        ))}
      </div>
      <button
        className={`px-4 py-2 rounded bg-blue-500 text-white text-sm font-medium hover:bg-blue-600 transition disabled:opacity-50`}
        disabled={selectedTypes.length === 0 || analysisLoading}
        onClick={handleAnalysis}
      >
        {analysisLoading ? '分析中...' : '智能分析'}
      </button>
    </div>
  );
};

export default BuoyListSidebar;