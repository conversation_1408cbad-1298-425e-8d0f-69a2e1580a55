import React, { useState, useEffect } from 'react';
import { useWebSocket } from '../../contexts/WebSocketContext';
import { useBuoyContext } from '../../contexts/BuoyContext';
import { useTheme } from '../../contexts/ThemeContext';

interface StatusChangeEvent {
  id: string;
  buoyId: string;
  timestamp: string;
  status: string;
  previousStatus?: string;
  source: string;
  type: string;
}

interface BuoyStatusMonitorProps {
  className?: string;
  maxEvents?: number;
}

/**
 * 浮标状态监控组件
 * 实时显示浮标状态变化事件
 */
export const BuoyStatusMonitor: React.FC<BuoyStatusMonitorProps> = ({
  className = '',
  maxEvents = 50
}) => {
  const { theme } = useTheme();
  const { buoys } = useBuoyContext();
  const { subscribe, unsubscribe, isConnected } = useWebSocket();
  const [statusEvents, setStatusEvents] = useState<StatusChangeEvent[]>([]);

  // 订阅全局状态变化
  useEffect(() => {
    if (!isConnected()) {
      return;
    }

    const handleStatusChange = (message: any) => {
      try {
        let parsedMessage = message;
        if (typeof message === 'string') {
          parsedMessage = JSON.parse(message);
        }

        if (parsedMessage.type === 'status_change' || parsedMessage.type === 'heartbeat') {
          const event: StatusChangeEvent = {
            id: `${parsedMessage.buoyId}-${Date.now()}`,
            buoyId: parsedMessage.buoyId,
            timestamp: parsedMessage.timestamp,
            status: parsedMessage.value,
            previousStatus: parsedMessage.previous_value,
            source: parsedMessage.source || 'unknown',
            type: parsedMessage.type
          };

          setStatusEvents(prev => {
            const newEvents = [event, ...prev];
            return newEvents.slice(0, maxEvents);
          });
        }
      } catch (error) {
        console.error('处理状态变化消息失败:', error);
      }
    };

    // 订阅全局状态变化主题
    const subscription = subscribe('/topic/buoys/status_changes', handleStatusChange);

    return () => {
      unsubscribe(subscription);
    };
  }, [subscribe, unsubscribe, isConnected, maxEvents]);

  // 获取浮标名称
  const getBuoyName = (buoyId: string) => {
    const buoy = buoys.find(b => b.id === buoyId);
    return buoy?.name || `浮标 ${buoyId}`;
  };

  // 获取状态显示文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '在线';
      case 'inactive':
        return '离线';
      case 'error':
        return '错误';
      default:
        return status;
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600';
      case 'inactive':
        return 'text-yellow-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  // 获取来源显示文本
  const getSourceText = (source: string) => {
    switch (source) {
      case 'heartbeat_timeout':
        return '心跳超时';
      case 'mqtt_lwt':
        return 'MQTT连接';
      case 'heartbeat':
        return '心跳';
      case 'heartbeat_new_buoy':
        return '新浮标';
      default:
        return source;
    }
  };

  // 格式化时间
  const formatTime = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch {
      return timestamp;
    }
  };

  return (
    <div className={`${className} ${theme === 'dark' ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'} rounded-lg shadow-md`}>
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold flex items-center">
          <span className={`mr-2 w-3 h-3 rounded-full ${isConnected() ? 'bg-green-500' : 'bg-red-500'}`}></span>
          浮标状态监控
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
          实时显示浮标状态变化事件 ({statusEvents.length}/{maxEvents})
        </p>
      </div>

      <div className="max-h-96 overflow-y-auto">
        {statusEvents.length === 0 ? (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            暂无状态变化事件
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {statusEvents.map((event) => (
              <div key={event.id} className="p-3 hover:bg-gray-50 dark:hover:bg-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">{getBuoyName(event.buoyId)}</span>
                      <span className={`text-sm ${getStatusColor(event.status)}`}>
                        {getStatusText(event.status)}
                      </span>
                      {event.previousStatus && event.previousStatus !== event.status && (
                        <span className="text-xs text-gray-500">
                          (从 {getStatusText(event.previousStatus)})
                        </span>
                      )}
                    </div>
                    <div className="flex items-center space-x-2 mt-1 text-xs text-gray-500 dark:text-gray-400">
                      <span>{getSourceText(event.source)}</span>
                      <span>•</span>
                      <span>{event.type === 'status_change' ? '状态变化' : '心跳'}</span>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {formatTime(event.timestamp)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {!isConnected() && (
        <div className="p-3 bg-red-50 dark:bg-red-900/20 border-t border-red-200 dark:border-red-800">
          <p className="text-sm text-red-600 dark:text-red-400">
            WebSocket未连接，无法接收实时状态更新
          </p>
        </div>
      )}
    </div>
  );
};

export default BuoyStatusMonitor;
